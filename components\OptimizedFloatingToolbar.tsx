import React, { useState, useEffect, useCallback } from 'react';
import { 
  Move, 
  RotateCw, 
  Lock, 
  Unlock, 
  AlignCenter, 
  Grid3X3, 
  Eye, 
  Undo, 
  Redo,
  GripVertical,
  Maximize,
  Minimize,
  Settings,
  Layers,
  ZoomIn,
  ZoomOut,
  X
} from 'lucide-react';
import { Button } from './ui/button';
import { Slider } from './ui/slider';
import { Separator } from './ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { useApp } from '../contexts/AppContext';
import { cn } from './ui/utils';

interface Position {
  x: number;
  y: number;
}

interface OptimizedFloatingToolbarProps {
  position: Position;
  onPositionChange: (position: Position) => void;
  selectedFloor: { buildingId: string; floor: any } | null;
  onOpacityChange: (opacity: number) => void;
  onLockToggle: () => void;
}

export function OptimizedFloatingToolbar({
  position,
  onPositionChange,
  selectedFloor,
  onOpacityChange,
  onLockToggle
}: OptimizedFloatingToolbarProps) {
  const { state, actions } = useApp();
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isExpanded, setIsExpanded] = useState(true);
  const [snapToGrid, setSnapToGrid] = useState(false);
  const [rotationMode, setRotationMode] = useState(false);
  const [history, setHistory] = useState<any[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Auto-hide when not in use
  const [isVisible, setIsVisible] = useState(true);
  const [lastInteraction, setLastInteraction] = useState(Date.now());

  useEffect(() => {
    const timer = setInterval(() => {
      if (Date.now() - lastInteraction > 5000 && !selectedFloor) {
        setIsVisible(false);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [lastInteraction, selectedFloor]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.target !== e.currentTarget) return;
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
    setLastInteraction(Date.now());
  }, [position]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;
    
    let newX = e.clientX - dragStart.x;
    let newY = e.clientY - dragStart.y;

    // Snap to edges
    const snapDistance = 20;
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    if (newX < snapDistance) newX = 10;
    if (newX > windowWidth - 300) newX = windowWidth - 300;
    if (newY < snapDistance) newY = 10;
    if (newY > windowHeight - 100) newY = windowHeight - 100;

    onPositionChange({ x: newX, y: newY });
  }, [isDragging, dragStart, onPositionChange]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const handleZoomIn = useCallback(() => {
    const newScale = Math.min(5, state.mapTransform.scale * 1.2);
    actions.setMapTransform({ ...state.mapTransform, scale: newScale });
    setLastInteraction(Date.now());
  }, [state.mapTransform, actions]);

  const handleZoomOut = useCallback(() => {
    const newScale = Math.max(0.1, state.mapTransform.scale / 1.2);
    actions.setMapTransform({ ...state.mapTransform, scale: newScale });
    setLastInteraction(Date.now());
  }, [state.mapTransform, actions]);

  const handleAlign = useCallback(() => {
    console.log('Auto-align floors');
    setLastInteraction(Date.now());
  }, []);

  const handleRotate = useCallback(() => {
    setRotationMode(!rotationMode);
    setLastInteraction(Date.now());
  }, [rotationMode]);

  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      // Apply previous state
    }
    setLastInteraction(Date.now());
  }, [historyIndex]);

  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      // Apply next state
    }
    setLastInteraction(Date.now());
  }, [historyIndex, history]);

  if (!isVisible && !selectedFloor) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-20 right-4 h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
        title="Show Tools"
      >
        <Settings className="h-5 w-5" />
      </Button>
    );
  }

  return (
    <TooltipProvider>
      <div
        className={cn(
          "fixed bg-card/95 backdrop-blur-sm border border-border rounded-lg shadow-xl transition-all duration-300",
          "hover:shadow-2xl hover:bg-card",
          isDragging && "shadow-2xl scale-105",
          isExpanded ? "p-3" : "p-2"
        )}
        style={{ left: position.x, top: position.y }}
        onMouseEnter={() => setLastInteraction(Date.now())}
      >
        {/* Drag Handle */}
        <div 
          className="flex items-center justify-between mb-2 cursor-move"
          onMouseDown={handleMouseDown}
        >
          <div className="flex items-center space-x-2">
            <GripVertical className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium">Tools</span>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="icon"
              className="w-6 h-6"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? <Minimize className="w-3 h-3" /> : <Maximize className="w-3 h-3" />}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="w-6 h-6"
              onClick={() => setIsVisible(false)}
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        </div>

        {isExpanded && (
          <>
            <div className="flex items-center space-x-1 mb-2">
              {/* Zoom Controls */}
              <div className="flex items-center space-x-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="w-8 h-8"
                      onClick={handleZoomOut}
                    >
                      <ZoomOut className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom Out</TooltipContent>
                </Tooltip>

                <div className="px-2 py-1 bg-muted rounded text-xs font-mono min-w-12 text-center">
                  {Math.round(state.mapTransform.scale * 100)}%
                </div>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="w-8 h-8"
                      onClick={handleZoomIn}
                    >
                      <ZoomIn className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom In</TooltipContent>
                </Tooltip>
              </div>

              <Separator orientation="vertical" className="h-8" />

              {/* Undo/Redo */}
              <div className="flex items-center space-x-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="w-8 h-8"
                      onClick={handleUndo}
                      disabled={historyIndex <= 0}
                    >
                      <Undo className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Undo (Ctrl+Z)</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="w-8 h-8"
                      onClick={handleRedo}
                      disabled={historyIndex >= history.length - 1}
                    >
                      <Redo className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Redo (Ctrl+Y)</TooltipContent>
                </Tooltip>
              </div>
            </div>

            {selectedFloor && (
              <>
                <Separator className="my-2" />
                
                <div className="flex items-center space-x-1 mb-2">
                  {/* Transform Tools */}
                  <div className="flex items-center space-x-1">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="w-8 h-8"
                          disabled={!selectedFloor}
                        >
                          <Move className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Move Tool</TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant={rotationMode ? "default" : "ghost"}
                          size="icon"
                          className="w-8 h-8"
                          onClick={handleRotate}
                        >
                          <RotateCw className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Rotate Tool</TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="w-8 h-8"
                          onClick={handleAlign}
                        >
                          <AlignCenter className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Auto Align</TooltipContent>
                    </Tooltip>
                  </div>

                  <Separator orientation="vertical" className="h-8" />

                  {/* Snap and Lock */}
                  <div className="flex items-center space-x-1">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant={snapToGrid ? "default" : "ghost"}
                          size="icon"
                          className="w-8 h-8"
                          onClick={() => setSnapToGrid(!snapToGrid)}
                        >
                          <Grid3X3 className="w-4 h-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Snap to Grid</TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="w-8 h-8"
                          onClick={onLockToggle}
                        >
                          {selectedFloor?.floor.locked ? (
                            <Lock className="w-4 h-4" />
                          ) : (
                            <Unlock className="w-4 h-4" />
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        {selectedFloor?.floor.locked ? 'Unlock Floor' : 'Lock Floor'}
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </div>

                {/* Opacity Control */}
                <div className="flex items-center space-x-2 p-2 bg-muted/50 rounded-md">
                  <Eye className="w-4 h-4 text-muted-foreground" />
                  <div className="flex-1">
                    <Slider
                      value={[selectedFloor.floor.opacity * 100]}
                      onValueChange={(value) => onOpacityChange(value[0] / 100)}
                      max={100}
                      min={0}
                      step={5}
                      className="w-full"
                    />
                  </div>
                  <span className="text-xs text-muted-foreground w-8 font-mono">
                    {Math.round(selectedFloor.floor.opacity * 100)}%
                  </span>
                </div>

                {/* Floor Info */}
                <div className="mt-2 pt-2 border-t border-border">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">{selectedFloor.floor.name}</div>
                      <div className="text-xs text-muted-foreground">
                        Level {selectedFloor.floor.level >= 0 ? selectedFloor.floor.level : `B${Math.abs(selectedFloor.floor.level)}`}
                        {selectedFloor.floor.locked && ' • Locked'}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => actions.setMetadataPanel(true)}
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </>
            )}

            {/* Advanced Tools Popover */}
            <Separator className="my-2" />
            <div className="flex justify-between items-center">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <Layers className="w-4 h-4 mr-2" />
                    Advanced
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80" side="right">
                  <div className="space-y-4">
                    <h4 className="font-medium">Advanced Tools</h4>
                    <div className="space-y-2">
                      <Button variant="ghost" className="w-full justify-start">
                        Batch Operations
                      </Button>
                      <Button variant="ghost" className="w-full justify-start">
                        Layer Management
                      </Button>
                      <Button variant="ghost" className="w-full justify-start">
                        Export Settings
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              <div className="text-xs text-muted-foreground">
                v1.0
              </div>
            </div>
          </>
        )}
      </div>
    </TooltipProvider>
  );
}