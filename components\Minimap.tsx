import React, { useRef, useEffect, useState } from 'react';
import { X } from 'lucide-react';
import { Button } from './ui/button';

interface Floor {
  id: string;
  name: string;
  level: number;
  visible: boolean;
  opacity: number;
  locked: boolean;
}

interface Building {
  id: string;
  name: string;
  floors: Floor[];
}

interface Transform {
  x: number;
  y: number;
  scale: number;
}

interface MinimapProps {
  buildings: Building[];
  transform: Transform;
  onTransformChange: (transform: Transform) => void;
  onToggle: () => void;
}

// Same mock data as MapCanvas for consistency
const mockFloorPlans = {
  'floor-1-1': { x: 100, y: 100, width: 200, height: 150, color: '#3b82f6' },
  'floor-1-2': { x: 120, y: 80, width: 200, height: 150, color: '#3b82f6' },
  'floor-1-3': { x: 140, y: 60, width: 200, height: 150, color: '#3b82f6' },
  'floor-2-1': { x: 400, y: 200, width: 180, height: 120, color: '#10b981' },
  'floor-2-2': { x: 420, y: 180, width: 180, height: 120, color: '#10b981' },
  'floor-3-1': { x: 250, y: 350, width: 160, height: 100, color: '#f59e0b' },
  'floor-3-2': { x: 270, y: 370, width: 160, height: 100, color: '#f59e0b' },
};

export function Minimap({ buildings, transform, onTransformChange, onToggle }: MinimapProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDragging, setIsDragging] = useState(false);

  // Calculate bounds of all visible floors
  const getBounds = () => {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    buildings.forEach(building => {
      building.floors.forEach(floor => {
        if (floor.visible) {
          const planData = mockFloorPlans[floor.id as keyof typeof mockFloorPlans];
          if (planData) {
            minX = Math.min(minX, planData.x);
            minY = Math.min(minY, planData.y);
            maxX = Math.max(maxX, planData.x + planData.width);
            maxY = Math.max(maxY, planData.y + planData.height);
          }
        }
      });
    });

    return { minX, minY, maxX, maxY };
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const bounds = getBounds();
    if (bounds.minX === Infinity) return;

    // Set canvas size
    const canvasWidth = 200;
    const canvasHeight = 150;
    canvas.width = canvasWidth;
    canvas.height = canvasHeight;

    // Calculate scale to fit all content
    const contentWidth = bounds.maxX - bounds.minX;
    const contentHeight = bounds.maxY - bounds.minY;
    const scale = Math.min(
      (canvasWidth - 20) / contentWidth,
      (canvasHeight - 20) / contentHeight
    );

    // Calculate offset to center content
    const offsetX = (canvasWidth - contentWidth * scale) / 2 - bounds.minX * scale;
    const offsetY = (canvasHeight - contentHeight * scale) / 2 - bounds.minY * scale;

    // Clear canvas
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // Apply minimap transform
    ctx.save();
    ctx.translate(offsetX, offsetY);
    ctx.scale(scale, scale);

    // Draw floor plans
    buildings.forEach(building => {
      building.floors.forEach(floor => {
        if (floor.visible) {
          const planData = mockFloorPlans[floor.id as keyof typeof mockFloorPlans];
          if (planData) {
            ctx.globalAlpha = 0.7;
            ctx.fillStyle = planData.color;
            ctx.fillRect(planData.x, planData.y, planData.width, planData.height);
            
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 1 / scale;
            ctx.strokeRect(planData.x, planData.y, planData.width, planData.height);
          }
        }
      });
    });

    ctx.restore();

    // Draw viewport indicator
    const viewportWidth = window.innerWidth / transform.scale * scale;
    const viewportHeight = window.innerHeight / transform.scale * scale;
    const viewportX = offsetX - (transform.x / transform.scale) * scale;
    const viewportY = offsetY - (transform.y / transform.scale) * scale;

    ctx.strokeStyle = '#ff6b6b';
    ctx.lineWidth = 2;
    ctx.setLineDash([3, 3]);
    ctx.strokeRect(viewportX, viewportY, viewportWidth, viewportHeight);
    ctx.setLineDash([]);

    // Draw viewport fill
    ctx.fillStyle = 'rgba(255, 107, 107, 0.1)';
    ctx.fillRect(viewportX, viewportY, viewportWidth, viewportHeight);

    ctx.globalAlpha = 1;
  }, [buildings, transform]);

  const handleCanvasClick = (e: React.MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const bounds = getBounds();
    if (bounds.minX === Infinity) return;

    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;
    const contentWidth = bounds.maxX - bounds.minX;
    const contentHeight = bounds.maxY - bounds.minY;
    const scale = Math.min(
      (canvasWidth - 20) / contentWidth,
      (canvasHeight - 20) / contentHeight
    );

    const offsetX = (canvasWidth - contentWidth * scale) / 2 - bounds.minX * scale;
    const offsetY = (canvasHeight - contentHeight * scale) / 2 - bounds.minY * scale;

    // Convert minimap coordinates to world coordinates
    const worldX = (x - offsetX) / scale;
    const worldY = (y - offsetY) / scale;

    // Center the main view on the clicked point
    const newTransform = {
      ...transform,
      x: window.innerWidth / 2 - worldX * transform.scale,
      y: window.innerHeight / 2 - worldY * transform.scale
    };

    onTransformChange(newTransform);
  };

  return (
    <div className="absolute bottom-4 right-4 bg-card border border-border rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-2 border-b border-border bg-muted/50">
        <span className="text-xs">Overview</span>
        <Button
          variant="ghost"
          size="icon"
          className="w-6 h-6"
          onClick={onToggle}
        >
          <X className="w-3 h-3" />
        </Button>
      </div>

      {/* Minimap Canvas */}
      <div className="relative">
        <canvas
          ref={canvasRef}
          className="cursor-pointer"
          onClick={handleCanvasClick}
        />
        
        {/* Legend */}
        <div className="absolute bottom-1 left-1 bg-background/80 rounded px-1 py-0.5">
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-red-500/50 border border-red-500 rounded-sm"></div>
            <span className="text-xs text-muted-foreground">Viewport</span>
          </div>
        </div>
      </div>

      {/* Scale Info */}
      <div className="p-2 border-t border-border bg-muted/50">
        <div className="text-xs text-muted-foreground">
          Zoom: {Math.round(transform.scale * 100)}%
        </div>
      </div>
    </div>
  );
}