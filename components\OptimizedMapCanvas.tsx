import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { useApp } from '../contexts/AppContext';
import { cn } from './ui/utils';

interface Floor {
  id: string;
  name: string;
  level: number;
  visible: boolean;
  opacity: number;
  locked: boolean;
}

interface Building {
  id: string;
  name: string;
  floors: Floor[];
}

interface Transform {
  x: number;
  y: number;
  scale: number;
}

// Enhanced mock floor plan data with better visualization
const mockFloorPlans = {
  'floor-1-1': { x: 100, y: 100, width: 200, height: 150, color: '#3b82f6', pattern: 'office' },
  'floor-1-2': { x: 120, y: 80, width: 200, height: 150, color: '#3b82f6', pattern: 'office' },
  'floor-1-3': { x: 140, y: 60, width: 200, height: 150, color: '#3b82f6', pattern: 'office' },
  'floor-2-1': { x: 400, y: 200, width: 180, height: 120, color: '#10b981', pattern: 'lab' },
  'floor-2-2': { x: 420, y: 180, width: 180, height: 120, color: '#10b981', pattern: 'lab' },
  'floor-3-1': { x: 250, y: 350, width: 160, height: 100, color: '#f59e0b', pattern: 'parking' },
  'floor-3-2': { x: 270, y: 370, width: 160, height: 100, color: '#f59e0b', pattern: 'parking' },
};

export function OptimizedMapCanvas() {
  const { state, actions } = useApp();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const [hoveredFloor, setHoveredFloor] = useState<string | null>(null);
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [draggedFloor, setDraggedFloor] = useState<string | null>(null);

  // Memoize visible floors for performance
  const visibleFloors = useMemo(() => {
    return state.buildings.flatMap(building => 
      building.floors
        .filter(floor => floor.visible)
        .map(floor => ({ ...floor, buildingId: building.id }))
    );
  }, [state.buildings]);

  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set high DPI canvas
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    canvas.style.width = rect.width + 'px';
    canvas.style.height = rect.height + 'px';
    ctx.scale(dpr, dpr);

    // Clear canvas
    ctx.clearRect(0, 0, rect.width, rect.height);

    // Apply transform
    ctx.save();
    ctx.translate(state.mapTransform.x, state.mapTransform.y);
    ctx.scale(state.mapTransform.scale, state.mapTransform.scale);

    // Draw grid
    drawGrid(ctx, rect.width, rect.height);

    // Draw floor plans with enhanced visuals
    visibleFloors.forEach(floor => {
      drawFloorPlan(ctx, floor);
    });

    // Highlight selected floor
    if (state.selectedFloor && state.selectedFloor.floor.visible) {
      highlightFloor(ctx, state.selectedFloor.floor);
    }

    // Highlight hovered floor
    if (hoveredFloor) {
      const floor = visibleFloors.find(f => f.id === hoveredFloor);
      if (floor) {
        drawFloorHover(ctx, floor);
      }
    }

    ctx.restore();

    // Draw UI overlays
    drawOverlays(ctx, rect.width, rect.height);
  }, [state.mapTransform, visibleFloors, state.selectedFloor, hoveredFloor]);

  const drawGrid = useCallback((ctx: CanvasRenderingContext2D, width: number, height: number) => {
    const gridSize = 50;
    const scaledGridSize = gridSize * state.mapTransform.scale;
    
    if (scaledGridSize < 10) return;

    const opacity = Math.min(1, scaledGridSize / 50);
    ctx.strokeStyle = `rgba(0, 0, 0, ${opacity * 0.1})`;
    ctx.lineWidth = 1 / state.mapTransform.scale;

    const startX = Math.floor((-state.mapTransform.x / state.mapTransform.scale) / gridSize) * gridSize;
    const startY = Math.floor((-state.mapTransform.y / state.mapTransform.scale) / gridSize) * gridSize;
    const endX = startX + (width / state.mapTransform.scale) + gridSize;
    const endY = startY + (height / state.mapTransform.scale) + gridSize;

    // Vertical lines
    for (let x = startX; x <= endX; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x, startY);
      ctx.lineTo(x, endY);
      ctx.stroke();
    }

    // Horizontal lines
    for (let y = startY; y <= endY; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(startX, y);
      ctx.lineTo(endX, y);
      ctx.stroke();
    }
  }, [state.mapTransform]);

  const drawFloorPlan = useCallback((ctx: CanvasRenderingContext2D, floor: Floor & { buildingId: string }) => {
    const planData = mockFloorPlans[floor.id as keyof typeof mockFloorPlans];
    if (!planData) return;

    ctx.globalAlpha = floor.opacity;

    // Draw shadow
    ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
    ctx.shadowBlur = 4 / state.mapTransform.scale;
    ctx.shadowOffsetX = 2 / state.mapTransform.scale;
    ctx.shadowOffsetY = 2 / state.mapTransform.scale;

    // Draw floor rectangle with gradient
    const gradient = ctx.createLinearGradient(
      planData.x, planData.y,
      planData.x + planData.width, planData.y + planData.height
    );
    gradient.addColorStop(0, planData.color);
    gradient.addColorStop(1, planData.color + '80');

    ctx.fillStyle = gradient;
    ctx.fillRect(planData.x, planData.y, planData.width, planData.height);

    // Draw pattern based on type
    drawFloorPattern(ctx, planData, floor);

    // Reset shadow
    ctx.shadowColor = 'transparent';

    // Draw border
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2 / state.mapTransform.scale;
    ctx.strokeRect(planData.x, planData.y, planData.width, planData.height);

    // Draw floor label
    ctx.fillStyle = '#ffffff';
    ctx.font = `${14 / state.mapTransform.scale}px Inter, sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(
      floor.name,
      planData.x + planData.width / 2,
      planData.y + planData.height / 2
    );

    // Draw level indicator
    ctx.font = `${12 / state.mapTransform.scale}px Inter, sans-serif`;
    ctx.fillText(
      floor.level >= 0 ? `L${floor.level}` : `B${Math.abs(floor.level)}`,
      planData.x + planData.width / 2,
      planData.y + planData.height / 2 + 20 / state.mapTransform.scale
    );

    // Draw lock indicator
    if (floor.locked) {
      ctx.fillStyle = '#fbbf24';
      ctx.font = `${16 / state.mapTransform.scale}px Inter, sans-serif`;
      ctx.fillText('🔒', planData.x + planData.width - 20 / state.mapTransform.scale, planData.y + 20 / state.mapTransform.scale);
    }

    ctx.globalAlpha = 1;
  }, [state.mapTransform.scale]);

  const drawFloorPattern = useCallback((ctx: CanvasRenderingContext2D, planData: any, floor: Floor) => {
    const pattern = planData.pattern;
    const patternSize = 10 / state.mapTransform.scale;
    
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 1 / state.mapTransform.scale;

    switch (pattern) {
      case 'office':
        // Draw office grid pattern
        for (let x = planData.x + patternSize; x < planData.x + planData.width; x += patternSize * 2) {
          for (let y = planData.y + patternSize; y < planData.y + planData.height; y += patternSize * 2) {
            ctx.strokeRect(x, y, patternSize, patternSize);
          }
        }
        break;
      case 'lab':
        // Draw lab equipment pattern
        for (let x = planData.x + patternSize; x < planData.x + planData.width; x += patternSize * 3) {
          ctx.beginPath();
          ctx.arc(x, planData.y + planData.height / 2, patternSize / 2, 0, Math.PI * 2);
          ctx.stroke();
        }
        break;
      case 'parking':
        // Draw parking spaces pattern
        const spaceWidth = patternSize * 2;
        for (let x = planData.x; x < planData.x + planData.width; x += spaceWidth) {
          ctx.beginPath();
          ctx.moveTo(x, planData.y);
          ctx.lineTo(x, planData.y + planData.height);
          ctx.stroke();
        }
        break;
    }
  }, [state.mapTransform.scale]);

  const highlightFloor = useCallback((ctx: CanvasRenderingContext2D, floor: Floor) => {
    const planData = mockFloorPlans[floor.id as keyof typeof mockFloorPlans];
    if (!planData) return;

    ctx.strokeStyle = '#ff6b6b';
    ctx.lineWidth = 3 / state.mapTransform.scale;
    ctx.setLineDash([5 / state.mapTransform.scale, 5 / state.mapTransform.scale]);
    ctx.strokeRect(planData.x - 2, planData.y - 2, planData.width + 4, planData.height + 4);
    ctx.setLineDash([]);

    // Draw selection corners
    const cornerSize = 8 / state.mapTransform.scale;
    ctx.fillStyle = '#ff6b6b';
    [
      [planData.x - cornerSize/2, planData.y - cornerSize/2],
      [planData.x + planData.width - cornerSize/2, planData.y - cornerSize/2],
      [planData.x - cornerSize/2, planData.y + planData.height - cornerSize/2],
      [planData.x + planData.width - cornerSize/2, planData.y + planData.height - cornerSize/2]
    ].forEach(([x, y]) => {
      ctx.fillRect(x, y, cornerSize, cornerSize);
    });
  }, [state.mapTransform.scale]);

  const drawFloorHover = useCallback((ctx: CanvasRenderingContext2D, floor: Floor) => {
    const planData = mockFloorPlans[floor.id as keyof typeof mockFloorPlans];
    if (!planData) return;

    ctx.strokeStyle = '#60a5fa';
    ctx.lineWidth = 2 / state.mapTransform.scale;
    ctx.setLineDash([3 / state.mapTransform.scale, 3 / state.mapTransform.scale]);
    ctx.strokeRect(planData.x - 1, planData.y - 1, planData.width + 2, planData.height + 2);
    ctx.setLineDash([]);
  }, [state.mapTransform.scale]);

  const drawOverlays = useCallback((ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // Draw cursor coordinates
    if (cursorPosition.x !== 0 || cursorPosition.y !== 0) {
      const worldX = (cursorPosition.x - state.mapTransform.x) / state.mapTransform.scale;
      const worldY = (cursorPosition.y - state.mapTransform.y) / state.mapTransform.scale;

      ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
      ctx.fillRect(10, height - 30, 150, 20);
      ctx.fillStyle = '#ffffff';
      ctx.font = '12px Inter, sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText(`${Math.round(worldX)}, ${Math.round(worldY)}`, 15, height - 15);
    }
  }, [cursorPosition, state.mapTransform]);

  // Optimized render loop
  useEffect(() => {
    const render = () => {
      drawCanvas();
      animationFrameRef.current = requestAnimationFrame(render);
    };
    render();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [drawCanvas]);

  const getFloorAtPoint = useCallback((x: number, y: number): { buildingId: string; floorId: string } | null => {
    const canvasX = (x - state.mapTransform.x) / state.mapTransform.scale;
    const canvasY = (y - state.mapTransform.y) / state.mapTransform.scale;

    for (const floor of visibleFloors) {
      const planData = mockFloorPlans[floor.id as keyof typeof mockFloorPlans];
      if (!planData) continue;

      if (
        canvasX >= planData.x &&
        canvasX <= planData.x + planData.width &&
        canvasY >= planData.y &&
        canvasY <= planData.y + planData.height
      ) {
        return { buildingId: floor.buildingId, floorId: floor.id };
      }
    }
    return null;
  }, [state.mapTransform, visibleFloors]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const floorAtPoint = getFloorAtPoint(x, y);
    
    if (floorAtPoint) {
      actions.selectFloor(floorAtPoint.buildingId, floorAtPoint.floorId);
      setDraggedFloor(floorAtPoint.floorId);
    }

    setIsDragging(true);
    setDragStart({ x: x - state.mapTransform.x, y: y - state.mapTransform.y });
    actions.setDragging(true);
  }, [state.mapTransform, getFloorAtPoint, actions]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    setCursorPosition({ x, y });

    if (isDragging) {
      actions.setMapTransform({
        ...state.mapTransform,
        x: x - dragStart.x,
        y: y - dragStart.y
      });
    } else {
      const floorAtPoint = getFloorAtPoint(x, y);
      setHoveredFloor(floorAtPoint?.floorId || null);
    }
  }, [isDragging, dragStart, state.mapTransform, getFloorAtPoint, actions]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDraggedFloor(null);
    actions.setDragging(false);
  }, [actions]);

  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
    const newScale = Math.max(0.1, Math.min(5, state.mapTransform.scale * zoomFactor));

    const newX = mouseX - (mouseX - state.mapTransform.x) * (newScale / state.mapTransform.scale);
    const newY = mouseY - (mouseY - state.mapTransform.y) * (newScale / state.mapTransform.scale);

    actions.setMapTransform({
      x: newX,
      y: newY,
      scale: newScale
    });
  }, [state.mapTransform, actions]);

  return (
    <div className="relative w-full h-full bg-muted/20 overflow-hidden">
      <canvas
        ref={canvasRef}
        className={cn(
          "w-full h-full transition-all duration-200",
          isDragging ? "cursor-grabbing" : hoveredFloor ? "cursor-pointer" : "cursor-grab"
        )}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onWheel={handleWheel}
      />
      
      {/* Enhanced Canvas Info */}
      <div className="absolute top-4 left-4 bg-card/95 backdrop-blur-sm border border-border rounded-lg p-3 shadow-lg">
        <div className="space-y-2">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <span className="text-muted-foreground">Zoom:</span>
              <span className="font-mono">{Math.round(state.mapTransform.scale * 100)}%</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-muted-foreground">Floors:</span>
              <span className="font-mono">{visibleFloors.length}</span>
            </div>
          </div>
          {state.selectedFloor && (
            <div className="flex items-center space-x-2 pt-2 border-t border-border">
              <span className="text-muted-foreground">Selected:</span>
              <span className="font-mono">{state.selectedFloor.floor.name}</span>
            </div>
          )}
        </div>
      </div>

      {/* Enhanced Instructions */}
      <div className="absolute bottom-4 left-4 bg-card/95 backdrop-blur-sm border border-border rounded-lg p-3 shadow-lg">
        <div className="space-y-2">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Click</kbd>
              <span className="text-sm">Select</span>
            </div>
            <div className="flex items-center space-x-2">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Drag</kbd>
              <span className="text-sm">Pan</span>
            </div>
            <div className="flex items-center space-x-2">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Scroll</kbd>
              <span className="text-sm">Zoom</span>
            </div>
          </div>
          {state.isDragging && (
            <div className="text-xs text-muted-foreground">
              Dragging: {draggedFloor ? 'Floor' : 'View'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}