import React, { useState, useEffect, useCallback } from 'react';
import { Search, Building2, Eye, EyeOff, Pin, PinOff, ChevronDown, ChevronRight, X, Filter, Check, Clock, Target } from 'lucide-react';
import { Input } from './ui/input';
import { Button } from './ui/button';
import { Separator } from './ui/separator';
import { Badge } from './ui/badge';
import { useApp } from '../contexts/AppContext';
import { cn } from './ui/utils';

interface Floor {
  id: string;
  name: string;
  level: number;
  visible: boolean;
  opacity: number;
  locked: boolean;
  isBaseFloor?: boolean;
  alignmentCompleted?: boolean;
  uploadedFile?: File | null;
}

interface Building {
  id: string;
  name: string;
  floors: Floor[];
  alignmentStatus: 'pending' | 'in-progress' | 'completed';
  baseFloorId?: string;
}

export function OptimizedSidebar() {
  const { state, actions, computed } = useApp();
  const [hoveredFloor, setHoveredFloor] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [levelFilter, setLevelFilter] = useState<'all' | 'above' | 'below'>('all');

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && !state.sidebarCollapsed) {
        actions.toggleSidebarCollapse();
      }
      if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        document.getElementById('sidebar-search')?.focus();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [state.sidebarCollapsed, actions]);

  const filteredBuildings = computed.filteredBuildings.map(building => ({
    ...building,
    floors: building.floors.filter(floor => {
      if (levelFilter === 'above') return floor.level >= 0;
      if (levelFilter === 'below') return floor.level < 0;
      return true;
    })
  })).filter(building => building.floors.length > 0);

  const getAlignmentStatusIcon = (building: Building) => {
    switch (building.alignmentStatus) {
      case 'completed':
        return <Check className="w-4 h-4 text-green-600" />;
      case 'in-progress':
        return <Target className="w-4 h-4 text-primary animate-pulse" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-muted-foreground" />;
      default:
        return <Building2 className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getAlignmentStatusBadge = (building: Building) => {
    switch (building.alignmentStatus) {
      case 'completed':
        return <Badge variant="default" className="text-xs bg-green-600">Completed</Badge>;
      case 'in-progress':
        return <Badge variant="default" className="text-xs">In Progress</Badge>;
      case 'pending':
        return <Badge variant="outline" className="text-xs">Pending</Badge>;
      default:
        return null;
    }
  };

  const FloorItem = useCallback(({ building, floor }: { building: Building; floor: Floor }) => (
    <div
      key={floor.id}
      className={cn(
        "flex items-center space-x-2 p-2 rounded-md group transition-all duration-200",
        "hover:bg-sidebar-accent/50 cursor-pointer",
        state.selectedFloor?.floor.id === floor.id && "bg-sidebar-accent border border-sidebar-border",
        hoveredFloor === floor.id && "shadow-sm",
        floor.isBaseFloor && "bg-primary/5 border border-primary/20"
      )}
      onMouseEnter={() => setHoveredFloor(floor.id)}
      onMouseLeave={() => setHoveredFloor(null)}
      onClick={() => actions.selectFloor(building.id, floor.id)}
    >
      <Button
        variant="ghost"
        size="icon"
        className="w-6 h-6 shrink-0"
        onClick={(e) => {
          e.stopPropagation();
          actions.toggleFloorVisibility(building.id, floor.id);
        }}
        title={floor.visible ? "Hide Floor" : "Show Floor"}
      >
        {floor.visible ? (
          <Eye className="w-3 h-3 text-primary" />
        ) : (
          <EyeOff className="w-3 h-3 text-muted-foreground" />
        )}
      </Button>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className={cn(
              "text-sm truncate transition-colors",
              floor.visible ? "text-sidebar-foreground" : "text-muted-foreground",
              floor.isBaseFloor && "font-medium"
            )}>
              {floor.name}
            </span>
            {floor.isBaseFloor && (
              <Badge variant="outline" className="text-xs px-1">BASE</Badge>
            )}
          </div>
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Badge 
              variant={floor.level >= 0 ? "default" : "secondary"} 
              className="text-xs px-1.5 py-0.5"
            >
              {floor.level >= 0 ? `L${floor.level}` : `B${Math.abs(floor.level)}`}
            </Badge>
          </div>
        </div>
        
        {/* Upload status and opacity indicator */}
        <div className="flex items-center space-x-2 mt-1">
          {floor.uploadedFile && (
            <Badge variant="outline" className="text-xs px-1">
              Uploaded
            </Badge>
          )}
          {floor.visible && (
            <div className="flex items-center space-x-1">
              <div className="w-12 bg-muted rounded-full h-1">
                <div 
                  className="bg-primary h-1 rounded-full transition-all duration-300"
                  style={{ width: `${floor.opacity * 100}%` }}
                />
              </div>
              <span className="text-xs text-muted-foreground">
                {Math.round(floor.opacity * 100)}%
              </span>
            </div>
          )}
        </div>
      </div>
      
      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
        {floor.locked && (
          <div className="w-3 h-3 bg-muted-foreground rounded-full" title="Locked" />
        )}
      </div>
    </div>
  ), [state.selectedFloor, hoveredFloor, actions]);

  const BuildingItem = useCallback(({ building }: { building: Building }) => (
    <div key={building.id} className={cn(
      "mb-2",
      state.currentAlignmentBuildingId === building.id && "ring-2 ring-primary ring-offset-2 rounded-lg"
    )}>
      <Button
        variant="ghost"
        className={cn(
          "w-full justify-start p-2 h-auto hover:bg-sidebar-accent/30 transition-colors",
          state.currentAlignmentBuildingId === building.id && "bg-primary/10"
        )}
        onClick={() => {
          actions.toggleBuildingExpansion(building.id);
          if (state.currentPage === 'align' && building.alignmentStatus !== 'completed') {
            actions.setCurrentAlignmentBuilding(building.id);
          }
        }}
      >
        <div className="flex items-center space-x-2 w-full">
          {state.expandedBuildings.has(building.id) ? (
            <ChevronDown className="w-4 h-4 text-muted-foreground transition-transform" />
          ) : (
            <ChevronRight className="w-4 h-4 text-muted-foreground transition-transform" />
          )}
          {getAlignmentStatusIcon(building)}
          <span className="text-left flex-1 truncate">{building.name}</span>
          <div className="flex items-center space-x-1">
            {state.currentPage === 'upload' ? (
              <Badge variant="outline" className="text-xs">
                {building.floors.filter(f => f.uploadedFile).length}/{building.floors.length} uploaded
              </Badge>
            ) : (
              getAlignmentStatusBadge(building)
            )}
            {building.floors.some(f => f.visible) && (
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
            )}
          </div>
        </div>
      </Button>

      {/* Floor List with Animation */}
      <div className={cn(
        "overflow-hidden transition-all duration-300 ease-in-out",
        state.expandedBuildings.has(building.id) ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
      )}>
        <div className="ml-6 mt-1 space-y-1">
          {building.floors.map((floor) => (
            <FloorItem key={floor.id} building={building} floor={floor} />
          ))}
        </div>
      </div>
    </div>
  ), [state.expandedBuildings, state.currentAlignmentBuildingId, state.currentPage, actions, FloorItem]);

  return (
    <div className={cn(
      "bg-sidebar border-r border-sidebar-border transition-all duration-300 flex relative",
      state.sidebarCollapsed ? "w-16" : "w-80"
    )}>
      {/* Icon-only sidebar when collapsed */}
      {state.sidebarCollapsed && (
        <div className="w-16 flex flex-col p-2 space-y-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={actions.toggleSidebarCollapse}
            className="w-12 h-12 relative"
            title="Expand Sidebar"
          >
            <Building2 className="w-5 h-5" />
            {computed.visibleFloorCount > 0 && (
              <Badge className="absolute -top-1 -right-1 w-5 h-5 p-0 flex items-center justify-center text-xs">
                {computed.visibleFloorCount}
              </Badge>
            )}
          </Button>
          
          <Separator />
          
          {filteredBuildings.slice(0, 4).map((building, index) => (
            <Button
              key={building.id}
              variant="ghost"
              size="icon"
              className={cn(
                "w-12 h-12 relative",
                state.currentAlignmentBuildingId === building.id && "ring-2 ring-primary"
              )}
              onClick={() => {
                actions.toggleSidebarCollapse();
                actions.toggleBuildingExpansion(building.id);
              }}
              title={building.name}
            >
              {getAlignmentStatusIcon(building)}
              {building.floors.some(f => f.visible) && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full animate-pulse" />
              )}
            </Button>
          ))}
        </div>
      )}

      {/* Expanded sidebar */}
      {!state.sidebarCollapsed && (
        <div className="flex-1 flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-sidebar-border">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h2 className="text-sidebar-foreground">Buildings & Floors</h2>
                {state.currentPage === 'align' && (
                  <p className="text-xs text-muted-foreground">
                    Sequential alignment mode
                  </p>
                )}
              </div>
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowFilters(!showFilters)}
                  className="w-8 h-8"
                  title="Toggle Filters"
                >
                  <Filter className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={actions.toggleSidebarPin}
                  className="w-8 h-8"
                  title={state.sidebarPinned ? "Unpin Sidebar" : "Pin Sidebar"}
                >
                  {state.sidebarPinned ? <Pin className="w-4 h-4" /> : <PinOff className="w-4 h-4" />}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={actions.toggleSidebarCollapse}
                  className="w-8 h-8"
                  title="Collapse Sidebar"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
            
            {/* Search */}
            <div className="relative mb-3">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                id="sidebar-search"
                placeholder="Search buildings and floors... (Ctrl+F)"
                value={state.searchTerm}
                onChange={(e) => actions.setSearchTerm(e.target.value)}
                className="pl-10 pr-8"
              />
              {state.searchTerm && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 w-6 h-6"
                  onClick={() => actions.setSearchTerm('')}
                >
                  <X className="w-3 h-3" />
                </Button>
              )}
            </div>

            {/* Filters */}
            {showFilters && (
              <div className="space-y-2 p-2 bg-sidebar-accent/30 rounded-md">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">Level:</span>
                  <div className="flex space-x-1">
                    {(['all', 'above', 'below'] as const).map((filter) => (
                      <Button
                        key={filter}
                        variant={levelFilter === filter ? "default" : "ghost"}
                        size="sm"
                        className="h-6 text-xs"
                        onClick={() => setLevelFilter(filter)}
                      >
                        {filter === 'all' ? 'All' : filter === 'above' ? 'Above Ground' : 'Below Ground'}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            )}
            
            {/* Stats */}
            <div className="flex items-center justify-between mt-3">
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">{computed.filteredBuildings.length} Buildings</Badge>
                <Badge variant="outline">{computed.visibleFloorCount} Visible</Badge>
                {state.currentPage === 'align' && (
                  <Badge variant="default">{computed.completedBuildings.length} Aligned</Badge>
                )}
              </div>
              <div className="text-xs text-muted-foreground">
                {computed.totalFloors} total floors
              </div>
            </div>
          </div>

          {/* Building List */}
          <div className="flex-1 overflow-y-auto p-2 space-y-1">
            {filteredBuildings.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Building2 className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No buildings found</p>
                {state.searchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => actions.setSearchTerm('')}
                    className="mt-2"
                  >
                    Clear search
                  </Button>
                )}
              </div>
            ) : (
              filteredBuildings.map((building) => (
                <BuildingItem key={building.id} building={building} />
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}