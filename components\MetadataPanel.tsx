import React, { useState, useEffect } from 'react';
import { X, Upload, Save, MapPin } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Separator } from './ui/separator';
import { Badge } from './ui/badge';

interface Floor {
  id: string;
  name: string;
  level: number;
  visible: boolean;
  opacity: number;
  locked: boolean;
}

interface MetadataPanelProps {
  open: boolean;
  selectedFloor: { buildingId: string; floor: Floor } | null;
  onClose: () => void;
  onUpdate: (updatedFloor: Partial<Floor>) => void;
}

export function MetadataPanel({ open, selectedFloor, onClose, onUpdate }: MetadataPanelProps) {
  const [formData, setFormData] = useState({
    name: '',
    level: 0,
    coordinates: { x: 0, y: 0 },
    rotation: 0,
    description: '',
    area: 0,
    buildingId: ''
  });

  useEffect(() => {
    if (selectedFloor) {
      setFormData({
        name: selectedFloor.floor.name,
        level: selectedFloor.floor.level,
        coordinates: { x: 100, y: 100 }, // Mock coordinates
        rotation: 0, // Mock rotation
        description: '', // Mock description
        area: 2500, // Mock area in sq ft
        buildingId: selectedFloor.buildingId
      });
    }
  }, [selectedFloor]);

  const handleSave = () => {
    if (!selectedFloor) return;

    onUpdate({
      name: formData.name,
      level: formData.level
    });
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      console.log('Uploading floor plan:', file.name);
      // TODO: Implement file upload logic
    }
  };

  const buildingNames = {
    'building-1': 'Main Office Building',
    'building-2': 'Research Lab',
    'building-3': 'Parking Garage'
  };

  if (!open || !selectedFloor) return null;

  return (
    <div className="fixed inset-y-0 right-0 w-80 bg-background border-l border-border shadow-xl transform transition-transform duration-300 ease-in-out z-50">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div>
          <h3>Floor Properties</h3>
          <p className="text-muted-foreground">
            {buildingNames[formData.buildingId as keyof typeof buildingNames]}
          </p>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h4>Basic Information</h4>
          
          <div className="space-y-2">
            <Label htmlFor="floor-name">Floor Name</Label>
            <Input
              id="floor-name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter floor name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="floor-level">Level</Label>
            <Select
              value={formData.level.toString()}
              onValueChange={(value) => setFormData({ ...formData, level: parseInt(value) })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="-2">B2 (Basement 2)</SelectItem>
                <SelectItem value="-1">B1 (Basement 1)</SelectItem>
                <SelectItem value="0">L0 (Ground Floor)</SelectItem>
                <SelectItem value="1">L1 (First Floor)</SelectItem>
                <SelectItem value="2">L2 (Second Floor)</SelectItem>
                <SelectItem value="3">L3 (Third Floor)</SelectItem>
                <SelectItem value="4">L4 (Fourth Floor)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Optional description"
              rows={3}
            />
          </div>
        </div>

        <Separator />

        {/* Positioning */}
        <div className="space-y-4">
          <h4>Positioning</h4>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="coord-x">X Coordinate</Label>
              <Input
                id="coord-x"
                type="number"
                value={formData.coordinates.x}
                onChange={(e) => setFormData({
                  ...formData,
                  coordinates: { ...formData.coordinates, x: parseFloat(e.target.value) || 0 }
                })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="coord-y">Y Coordinate</Label>
              <Input
                id="coord-y"
                type="number"
                value={formData.coordinates.y}
                onChange={(e) => setFormData({
                  ...formData,
                  coordinates: { ...formData.coordinates, y: parseFloat(e.target.value) || 0 }
                })}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="rotation">Rotation (degrees)</Label>
            <Input
              id="rotation"
              type="number"
              value={formData.rotation}
              onChange={(e) => setFormData({ ...formData, rotation: parseFloat(e.target.value) || 0 })}
              min="0"
              max="360"
            />
          </div>
        </div>

        <Separator />

        {/* Floor Plan */}
        <div className="space-y-4">
          <h4>Floor Plan</h4>
          
          <div className="space-y-2">
            <Label>Upload New Floor Plan</Label>
            <div className="border-2 border-dashed border-border rounded-lg p-4 text-center">
              <input
                type="file"
                accept="image/*,.dwg,.dxf,.pdf"
                onChange={handleFileUpload}
                className="hidden"
                id="file-upload"
              />
              <label htmlFor="file-upload" className="cursor-pointer">
                <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-muted-foreground">
                  Click to upload floor plan
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Supports: JPG, PNG, DWG, DXF, PDF
                </p>
              </label>
            </div>
          </div>
        </div>

        <Separator />

        {/* Metadata */}
        <div className="space-y-4">
          <h4>Metadata</h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Floor Area</span>
              <Badge variant="outline">{formData.area.toLocaleString()} sq ft</Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Status</span>
              <div className="flex items-center space-x-2">
                <Badge variant={selectedFloor.floor.visible ? "default" : "secondary"}>
                  {selectedFloor.floor.visible ? "Visible" : "Hidden"}
                </Badge>
                {selectedFloor.floor.locked && (
                  <Badge variant="destructive">Locked</Badge>
                )}
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Opacity</span>
              <Badge variant="outline">
                {Math.round(selectedFloor.floor.opacity * 100)}%
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t border-border p-4">
        <div className="flex items-center space-x-2">
          <Button onClick={handleSave} className="flex-1">
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </Button>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}