import React, { useEffect } from 'react';
import { OptimizedSidebar } from './OptimizedSidebar';
import { OptimizedMapCanvas } from './OptimizedMapCanvas';
import { OptimizedFloatingToolbar } from './OptimizedFloatingToolbar';
import { Minimap } from './Minimap';
import { MetadataPanel } from './MetadataPanel';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { Badge } from './ui/badge';
import { Card, CardContent } from './ui/card';
import { useApp } from '../contexts/AppContext';
import { toast } from 'sonner@2.0.3';
import { 
  Map, 
  X, 
  ArrowLeft, 
  ArrowRight, 
  Check, 
  Building2,
  Target,
  Layers
} from 'lucide-react';

export function AlignmentPage() {
  const { state, actions, computed } = useApp();

  // Auto-show base floors of completed buildings
  useEffect(() => {
    computed.completedBuildings.forEach(building => {
      if (building.baseFloorId) {
        const baseFloor = building.floors.find(f => f.id === building.baseFloorId);
        if (baseFloor && !baseFloor.visible) {
          actions.toggleFloorVisibility(building.id, building.baseFloorId);
        }
      }
    });
  }, [computed.completedBuildings, actions]);

  // Keyboard shortcuts for alignment workflow
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'n':
            e.preventDefault();
            handleNextBuilding();
            break;
          case 'Enter':
            e.preventDefault();
            handleCompleteCurrentBuilding();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [state.currentAlignmentBuildingId]);

  const handleCompleteCurrentBuilding = () => {
    if (!state.currentAlignmentBuildingId) return;
    
    const currentBuilding = computed.currentAlignmentBuilding;
    if (!currentBuilding) return;

    // Ensure a base floor is selected
    if (!currentBuilding.baseFloorId) {
      toast.error('Please select a base floor before completing alignment');
      return;
    }

    actions.completeBuildingAlignment(state.currentAlignmentBuildingId);
    
    if (computed.isAlignmentComplete) {
      toast.success('All buildings aligned successfully!');
    } else {
      toast.success(`${currentBuilding.name} alignment completed`);
    }
  };

  const handleNextBuilding = () => {
    const currentIndex = state.alignmentQueue.indexOf(state.currentAlignmentBuildingId || '');
    const nextBuildingId = state.alignmentQueue[currentIndex + 1];
    
    if (nextBuildingId) {
      actions.setCurrentAlignmentBuilding(nextBuildingId);
    }
  };

  const handlePreviousBuilding = () => {
    const currentIndex = state.alignmentQueue.indexOf(state.currentAlignmentBuildingId || '');
    const prevBuildingId = state.alignmentQueue[currentIndex - 1];
    
    if (prevBuildingId) {
      actions.setCurrentAlignmentBuilding(prevBuildingId);
    }
  };

  const handleSetBaseFloor = (floorId: string) => {
    if (!state.currentAlignmentBuildingId) return;
    actions.setBaseFloor(state.currentAlignmentBuildingId, floorId);
    toast.success('Base floor set successfully');
  };

  const currentBuildingIndex = state.alignmentQueue.indexOf(state.currentAlignmentBuildingId || '') + 1;

  return (
    <div className="h-screen w-full bg-background flex overflow-hidden">
      {/* Alignment Progress Header */}
      <div className="absolute top-0 left-0 right-0 z-50 bg-card/95 backdrop-blur-sm border-b border-border">
        <div className="px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => actions.setPage('upload')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Upload
              </Button>
              
              <div className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-primary" />
                <span className="font-medium">Alignment Mode</span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-sm text-muted-foreground">
                Building {currentBuildingIndex} of {state.totalBuildingsToAlign}
              </div>
              <div className="w-48">
                <Progress value={state.alignmentProgress} className="h-2" />
              </div>
              <Badge variant="outline">
                {Math.round(state.alignmentProgress)}% Complete
              </Badge>
            </div>
          </div>

          {/* Current Building Info */}
          {computed.currentAlignmentBuilding && (
            <div className="mt-3 p-3 bg-primary/5 rounded-lg border border-primary/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Building2 className="w-5 h-5 text-primary" />
                  <div>
                    <div className="font-medium">{computed.currentAlignmentBuilding.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {computed.currentAlignmentBuilding.floors.length} floors • 
                      {computed.currentAlignmentBuilding.baseFloorId ? ' Base floor set' : ' No base floor selected'}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePreviousBuilding}
                    disabled={currentBuildingIndex <= 1}
                  >
                    Previous
                  </Button>
                  
                  <Button
                    onClick={handleCompleteCurrentBuilding}
                    disabled={!computed.currentAlignmentBuilding.baseFloorId}
                    className="px-6"
                  >
                    <Check className="w-4 h-4 mr-2" />
                    Complete Alignment
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNextBuilding}
                    disabled={currentBuildingIndex >= state.totalBuildingsToAlign}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Sidebar with alignment-specific features */}
      <div className="mt-32">
        <OptimizedSidebar />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 relative mt-32">
        {/* Map Canvas */}
        <OptimizedMapCanvas />

        {/* Floating Toolbar */}
        <OptimizedFloatingToolbar
          position={state.toolbarPosition}
          onPositionChange={actions.setToolbarPosition}
          selectedFloor={state.selectedFloor}
          onOpacityChange={(opacity) => {
            if (state.selectedFloor) {
              actions.updateFloorOpacity(
                state.selectedFloor.buildingId, 
                state.selectedFloor.floor.id, 
                opacity
              );
            }
          }}
          onLockToggle={() => {
            if (state.selectedFloor) {
              actions.toggleFloorLock(
                state.selectedFloor.buildingId, 
                state.selectedFloor.floor.id
              );
            }
          }}
        />

        {/* Base Floor Selector */}
        {computed.currentAlignmentBuilding && (
          <Card className="absolute top-4 left-4 w-80">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 mb-3">
                <Layers className="w-4 h-4" />
                <h4>Select Base Floor</h4>
              </div>
              <p className="text-sm text-muted-foreground mb-3">
                Choose the reference floor for {computed.currentAlignmentBuilding.name}
              </p>
              <div className="space-y-2">
                {computed.currentAlignmentBuilding.floors.map((floor) => (
                  <Button
                    key={floor.id}
                    variant={computed.currentAlignmentBuilding!.baseFloorId === floor.id ? "default" : "outline"}
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => handleSetBaseFloor(floor.id)}
                  >
                    <div className="flex items-center justify-between w-full">
                      <span>{floor.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        L{floor.level >= 0 ? floor.level : `B${Math.abs(floor.level)}`}
                      </Badge>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Completed Buildings Status */}
        {computed.completedBuildings.length > 0 && (
          <Card className="absolute bottom-4 left-4 w-80">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 mb-3">
                <Check className="w-4 h-4 text-green-600" />
                <h4>Completed Buildings</h4>
              </div>
              <div className="space-y-2">
                {computed.completedBuildings.map((building) => (
                  <div key={building.id} className="flex items-center justify-between p-2 bg-green-50 dark:bg-green-950/20 rounded-md">
                    <div className="flex items-center space-x-2">
                      <Building2 className="w-4 h-4 text-green-600" />
                      <span className="text-sm">{building.name}</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      Base floor visible
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Minimap */}
        {state.showMinimap && (
          <Minimap
            buildings={state.buildings}
            transform={state.mapTransform}
            onTransformChange={actions.setMapTransform}
            onToggle={actions.toggleMinimap}
          />
        )}

        {/* Minimap Toggle Button */}
        {!state.showMinimap && (
          <Button
            onClick={actions.toggleMinimap}
            className="fixed bottom-4 right-4 h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
            title="Show Minimap (Ctrl+M)"
          >
            <Map className="h-5 w-5" />
          </Button>
        )}

        {/* Keyboard Shortcuts Help */}
        <div className="absolute top-4 right-4 bg-card/95 backdrop-blur-sm border border-border rounded-lg p-3 shadow-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Alignment Shortcuts</span>
          </div>
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl+Enter</kbd>
              <span className="text-sm text-muted-foreground">Complete Building</span>
            </div>
            <div className="flex items-center space-x-2">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl+N</kbd>
              <span className="text-sm text-muted-foreground">Next Building</span>
            </div>
            <div className="flex items-center space-x-2">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Click</kbd>
              <span className="text-sm text-muted-foreground">Select Floor</span>
            </div>
          </div>
        </div>

        {/* Loading Indicator */}
        {state.isLoading && (
          <div className="absolute inset-0 bg-background/50 backdrop-blur-sm flex items-center justify-center">
            <div className="bg-card border border-border rounded-lg p-6 shadow-lg">
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span>Processing alignment...</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Metadata Panel */}
      <MetadataPanel
        open={state.metadataPanelOpen}
        selectedFloor={state.selectedFloor}
        onClose={() => actions.setMetadataPanel(false)}
        onUpdate={(updatedFloor) => {
          if (state.selectedFloor) {
            actions.updateFloor(
              state.selectedFloor.buildingId,
              state.selectedFloor.floor.id,
              updatedFloor
            );
          }
        }}
      />
    </div>
  );
}