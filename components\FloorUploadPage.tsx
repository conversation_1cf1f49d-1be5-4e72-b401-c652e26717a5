import React, { useState, useCallback } from 'react';
import { 
  Upload, 
  Building2, 
  Plus, 
  Trash2, 
  FileText, 
  ArrowRight, 
  Check,
  AlertCircle
} from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { useApp } from '../contexts/AppContext';
import { toast } from 'sonner@2.0.3';

interface NewBuilding {
  name: string;
  floors: {
    name: string;
    level: number;
  }[];
}

export function FloorUploadPage() {
  const { state, actions, computed } = useApp();
  const [newBuilding, setNewBuilding] = useState<NewBuilding>({
    name: '',
    floors: [{ name: 'Ground Floor', level: 0 }]
  });
  const [showAddBuildingDialog, setShowAddBuildingDialog] = useState(false);

  const handleFileUpload = useCallback((buildingId: string, floorId: string, files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    const validTypes = ['image/jpeg', 'image/png', 'image/svg+xml', 'application/pdf'];
    
    if (!validTypes.includes(file.type)) {
      toast.error('Please upload a valid image file (JPG, PNG, SVG) or PDF');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      toast.error('File size must be less than 10MB');
      return;
    }

    actions.uploadFloorPlan(buildingId, floorId, file);
    toast.success(`Floor plan uploaded for ${file.name}`);
  }, [actions]);

  const addFloorToNewBuilding = () => {
    setNewBuilding(prev => ({
      ...prev,
      floors: [...prev.floors, { 
        name: `Floor ${prev.floors.length + 1}`, 
        level: prev.floors.length 
      }]
    }));
  };

  const removeFloorFromNewBuilding = (index: number) => {
    if (newBuilding.floors.length <= 1) return;
    setNewBuilding(prev => ({
      ...prev,
      floors: prev.floors.filter((_, i) => i !== index)
    }));
  };

  const createBuilding = () => {
    if (!newBuilding.name.trim()) {
      toast.error('Please enter a building name');
      return;
    }

    const building = {
      id: `building-${Date.now()}`,
      name: newBuilding.name,
      alignmentStatus: 'pending' as const,
      floors: newBuilding.floors.map((floor, index) => ({
        id: `floor-${Date.now()}-${index}`,
        name: floor.name,
        level: floor.level,
        visible: false,
        opacity: 0.8,
        locked: false,
        uploadedFile: null,
        uploadedAt: undefined
      }))
    };

    actions.addBuilding(building);
    setNewBuilding({ name: '', floors: [{ name: 'Ground Floor', level: 0 }] });
    setShowAddBuildingDialog(false);
    toast.success(`Building "${building.name}" created successfully`);
  };

  const getUploadedFloorsCount = (building: any) => {
    return building.floors.filter((floor: any) => floor.uploadedFile).length;
  };

  const getTotalFloorsWithUploads = () => {
    return state.buildings.reduce((total, building) => 
      total + building.floors.filter(floor => floor.uploadedFile).length, 0
    );
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1>Floor Plan Upload</h1>
              <p className="text-muted-foreground">
                Upload floor plans for your buildings before starting the alignment process
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="outline" className="px-3 py-1">
                {state.buildings.length} Buildings
              </Badge>
              <Badge variant="outline" className="px-3 py-1">
                {getTotalFloorsWithUploads()} Floors Uploaded
              </Badge>
              <Button
                onClick={actions.startAlignmentWorkflow}
                disabled={!computed.canStartAlignment}
                className="px-6"
              >
                <ArrowRight className="w-4 h-4 mr-2" />
                Start Alignment
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-6 py-8">
        {/* Add Building Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2>Buildings</h2>
            <Dialog open={showAddBuildingDialog} onOpenChange={setShowAddBuildingDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Building
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Add New Building</DialogTitle>
                  <DialogDescription>
                    Create a new building and define its floors
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="building-name">Building Name</Label>
                    <Input
                      id="building-name"
                      value={newBuilding.name}
                      onChange={(e) => setNewBuilding(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter building name"
                    />
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Label>Floors</Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={addFloorToNewBuilding}
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        Add Floor
                      </Button>
                    </div>
                    
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {newBuilding.floors.map((floor, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Input
                            value={floor.name}
                            onChange={(e) => setNewBuilding(prev => ({
                              ...prev,
                              floors: prev.floors.map((f, i) => 
                                i === index ? { ...f, name: e.target.value } : f
                              )
                            }))}
                            placeholder="Floor name"
                            className="flex-1"
                          />
                          <Select
                            value={floor.level.toString()}
                            onValueChange={(value) => setNewBuilding(prev => ({
                              ...prev,
                              floors: prev.floors.map((f, i) => 
                                i === index ? { ...f, level: parseInt(value) } : f
                              )
                            }))}
                          >
                            <SelectTrigger className="w-24">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {Array.from({ length: 11 }, (_, i) => i - 2).map(level => (
                                <SelectItem key={level} value={level.toString()}>
                                  {level >= 0 ? `L${level}` : `B${Math.abs(level)}`}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {newBuilding.floors.length > 1 && (
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => removeFloorFromNewBuilding(index)}
                              className="w-8 h-8"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowAddBuildingDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={createBuilding}>
                    Create Building
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Buildings Grid */}
          {state.buildings.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <Building2 className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <h3>No Buildings Yet</h3>
                <p className="text-muted-foreground mb-4">
                  Start by adding your first building and its floors
                </p>
                <Button onClick={() => setShowAddBuildingDialog(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add First Building
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {state.buildings.map((building) => (
                <Card key={building.id} className="relative">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="flex items-center space-x-2">
                          <Building2 className="w-5 h-5" />
                          <span>{building.name}</span>
                        </CardTitle>
                        <CardDescription>
                          {building.floors.length} floors
                        </CardDescription>
                      </div>
                      <Badge variant={getUploadedFloorsCount(building) === building.floors.length ? "default" : "secondary"}>
                        {getUploadedFloorsCount(building)}/{building.floors.length} uploaded
                      </Badge>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-3">
                      {building.floors.map((floor) => (
                        <div key={floor.id} className="flex items-center justify-between p-2 border border-border rounded-md">
                          <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-muted rounded flex items-center justify-center">
                              {floor.uploadedFile ? (
                                <Check className="w-4 h-4 text-green-600" />
                              ) : (
                                <FileText className="w-4 h-4 text-muted-foreground" />
                              )}
                            </div>
                            <div>
                              <div className="text-sm font-medium">{floor.name}</div>
                              <div className="text-xs text-muted-foreground">
                                Level {floor.level >= 0 ? floor.level : `B${Math.abs(floor.level)}`}
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            {floor.uploadedFile ? (
                              <Badge variant="outline" className="text-xs">
                                {floor.uploadedFile.name.length > 20 
                                  ? `${floor.uploadedFile.name.substring(0, 20)}...` 
                                  : floor.uploadedFile.name
                                }
                              </Badge>
                            ) : (
                              <label className="cursor-pointer">
                                <input
                                  type="file"
                                  accept="image/*,.pdf"
                                  className="hidden"
                                  onChange={(e) => handleFileUpload(building.id, floor.id, e.target.files)}
                                />
                                <Button variant="outline" size="sm" className="pointer-events-none">
                                  <Upload className="w-3 h-3 mr-1" />
                                  Upload
                                </Button>
                              </label>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Upload Instructions */}
        <Card className="bg-muted/50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5" />
              <span>Upload Guidelines</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="mb-2">Supported Formats</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• JPG, PNG, SVG images</li>
                  <li>• PDF documents</li>
                  <li>• Maximum file size: 10MB</li>
                </ul>
              </div>
              <div>
                <h4 className="mb-2">Best Practices</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Upload high-resolution plans</li>
                  <li>• Ensure plans are oriented correctly</li>
                  <li>• Name floors consistently</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}