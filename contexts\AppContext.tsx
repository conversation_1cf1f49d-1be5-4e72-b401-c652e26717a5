import React, { createContext, useContext, useReducer, useCallback, useMemo } from 'react';

interface Floor {
  id: string;
  name: string;
  level: number;
  visible: boolean;
  opacity: number;
  locked: boolean;
  isBaseFloor?: boolean;
  alignmentCompleted?: boolean;
  uploadedFile?: File | null;
  uploadedAt?: Date;
}

interface Building {
  id: string;
  name: string;
  floors: Floor[];
  alignmentStatus: 'pending' | 'in-progress' | 'completed';
  baseFloorId?: string;
}

interface Transform {
  x: number;
  y: number;
  scale: number;
}

type AppPage = 'upload' | 'align';

interface AppState {
  currentPage: AppPage;
  buildings: Building[];
  selectedFloor: { buildingId: string; floor: Floor } | null;
  sidebarCollapsed: boolean;
  sidebarPinned: boolean;
  showMinimap: boolean;
  metadataPanelOpen: boolean;
  toolbarPosition: { x: number; y: number };
  mapTransform: Transform;
  searchTerm: string;
  expandedBuildings: Set<string>;
  isDragging: boolean;
  isLoading: boolean;
  
  // Alignment workflow state
  currentAlignmentBuildingId: string | null;
  alignmentQueue: string[];
  alignmentProgress: number;
  totalBuildingsToAlign: number;
}

type AppAction = 
  | { type: 'SET_PAGE'; payload: AppPage }
  | { type: 'SET_BUILDINGS'; payload: Building[] }
  | { type: 'ADD_BUILDING'; payload: Building }
  | { type: 'SELECT_FLOOR'; payload: { buildingId: string; floor: Floor } | null }
  | { type: 'TOGGLE_SIDEBAR_COLLAPSE' }
  | { type: 'TOGGLE_SIDEBAR_PIN' }
  | { type: 'TOGGLE_MINIMAP' }
  | { type: 'SET_METADATA_PANEL'; payload: boolean }
  | { type: 'SET_TOOLBAR_POSITION'; payload: { x: number; y: number } }
  | { type: 'SET_MAP_TRANSFORM'; payload: Transform }
  | { type: 'SET_SEARCH_TERM'; payload: string }
  | { type: 'TOGGLE_BUILDING_EXPANSION'; payload: string }
  | { type: 'TOGGLE_FLOOR_VISIBILITY'; payload: { buildingId: string; floorId: string } }
  | { type: 'UPDATE_FLOOR_OPACITY'; payload: { buildingId: string; floorId: string; opacity: number } }
  | { type: 'TOGGLE_FLOOR_LOCK'; payload: { buildingId: string; floorId: string } }
  | { type: 'SET_DRAGGING'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'UPDATE_FLOOR'; payload: { buildingId: string; floorId: string; updates: Partial<Floor> } }
  | { type: 'UPLOAD_FLOOR_PLAN'; payload: { buildingId: string; floorId: string; file: File } }
  | { type: 'START_ALIGNMENT_WORKFLOW' }
  | { type: 'COMPLETE_BUILDING_ALIGNMENT'; payload: string }
  | { type: 'SET_CURRENT_ALIGNMENT_BUILDING'; payload: string | null }
  | { type: 'SET_BASE_FLOOR'; payload: { buildingId: string; floorId: string } };

const initialState: AppState = {
  currentPage: 'upload',
  buildings: [],
  selectedFloor: null,
  sidebarCollapsed: false,
  sidebarPinned: true,
  showMinimap: true,
  metadataPanelOpen: false,
  toolbarPosition: { x: 20, y: 20 },
  mapTransform: { x: 0, y: 0, scale: 1 },
  searchTerm: '',
  expandedBuildings: new Set(),
  isDragging: false,
  isLoading: false,
  currentAlignmentBuildingId: null,
  alignmentQueue: [],
  alignmentProgress: 0,
  totalBuildingsToAlign: 0,
};

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_PAGE':
      return { ...state, currentPage: action.payload };
    
    case 'SET_BUILDINGS':
      return { ...state, buildings: action.payload };
    
    case 'ADD_BUILDING':
      return { 
        ...state, 
        buildings: [...state.buildings, action.payload],
        expandedBuildings: new Set([...state.expandedBuildings, action.payload.id])
      };
    
    case 'SELECT_FLOOR':
      return { 
        ...state, 
        selectedFloor: action.payload,
        metadataPanelOpen: action.payload !== null
      };
    
    case 'TOGGLE_SIDEBAR_COLLAPSE':
      return { ...state, sidebarCollapsed: !state.sidebarCollapsed };
    
    case 'TOGGLE_SIDEBAR_PIN':
      return { ...state, sidebarPinned: !state.sidebarPinned };
    
    case 'TOGGLE_MINIMAP':
      return { ...state, showMinimap: !state.showMinimap };
    
    case 'SET_METADATA_PANEL':
      return { ...state, metadataPanelOpen: action.payload };
    
    case 'SET_TOOLBAR_POSITION':
      return { ...state, toolbarPosition: action.payload };
    
    case 'SET_MAP_TRANSFORM':
      return { ...state, mapTransform: action.payload };
    
    case 'SET_SEARCH_TERM':
      return { ...state, searchTerm: action.payload };
    
    case 'TOGGLE_BUILDING_EXPANSION':
      const newExpanded = new Set(state.expandedBuildings);
      if (newExpanded.has(action.payload)) {
        newExpanded.delete(action.payload);
      } else {
        newExpanded.add(action.payload);
      }
      return { ...state, expandedBuildings: newExpanded };
    
    case 'TOGGLE_FLOOR_VISIBILITY':
      return {
        ...state,
        buildings: state.buildings.map(building =>
          building.id === action.payload.buildingId
            ? {
                ...building,
                floors: building.floors.map(floor =>
                  floor.id === action.payload.floorId
                    ? { ...floor, visible: !floor.visible }
                    : floor
                )
              }
            : building
        )
      };
    
    case 'UPDATE_FLOOR_OPACITY':
      return {
        ...state,
        buildings: state.buildings.map(building =>
          building.id === action.payload.buildingId
            ? {
                ...building,
                floors: building.floors.map(floor =>
                  floor.id === action.payload.floorId
                    ? { ...floor, opacity: action.payload.opacity }
                    : floor
                )
              }
            : building
        )
      };
    
    case 'TOGGLE_FLOOR_LOCK':
      return {
        ...state,
        buildings: state.buildings.map(building =>
          building.id === action.payload.buildingId
            ? {
                ...building,
                floors: building.floors.map(floor =>
                  floor.id === action.payload.floorId
                    ? { ...floor, locked: !floor.locked }
                    : floor
                )
              }
            : building
        )
      };
    
    case 'SET_DRAGGING':
      return { ...state, isDragging: action.payload };
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'UPDATE_FLOOR':
      return {
        ...state,
        buildings: state.buildings.map(building =>
          building.id === action.payload.buildingId
            ? {
                ...building,
                floors: building.floors.map(floor =>
                  floor.id === action.payload.floorId
                    ? { ...floor, ...action.payload.updates }
                    : floor
                )
              }
            : building
        )
      };
    
    case 'UPLOAD_FLOOR_PLAN':
      return {
        ...state,
        buildings: state.buildings.map(building =>
          building.id === action.payload.buildingId
            ? {
                ...building,
                floors: building.floors.map(floor =>
                  floor.id === action.payload.floorId
                    ? { 
                        ...floor, 
                        uploadedFile: action.payload.file,
                        uploadedAt: new Date()
                      }
                    : floor
                )
              }
            : building
        )
      };
    
    case 'START_ALIGNMENT_WORKFLOW':
      const buildingsWithUploads = state.buildings.filter(building => 
        building.floors.some(floor => floor.uploadedFile)
      );
      return {
        ...state,
        currentPage: 'align',
        alignmentQueue: buildingsWithUploads.map(b => b.id),
        currentAlignmentBuildingId: buildingsWithUploads[0]?.id || null,
        totalBuildingsToAlign: buildingsWithUploads.length,
        alignmentProgress: 0,
        buildings: state.buildings.map(building => ({
          ...building,
          alignmentStatus: buildingsWithUploads.some(b => b.id === building.id) ? 'pending' : building.alignmentStatus
        }))
      };
    
    case 'COMPLETE_BUILDING_ALIGNMENT':
      const currentIndex = state.alignmentQueue.indexOf(action.payload);
      const nextBuildingId = state.alignmentQueue[currentIndex + 1] || null;
      
      return {
        ...state,
        buildings: state.buildings.map(building =>
          building.id === action.payload
            ? { ...building, alignmentStatus: 'completed' }
            : building.id === nextBuildingId
            ? { ...building, alignmentStatus: 'in-progress' }
            : building
        ),
        currentAlignmentBuildingId: nextBuildingId,
        alignmentProgress: ((currentIndex + 1) / state.totalBuildingsToAlign) * 100
      };
    
    case 'SET_CURRENT_ALIGNMENT_BUILDING':
      return {
        ...state,
        currentAlignmentBuildingId: action.payload,
        buildings: state.buildings.map(building => ({
          ...building,
          alignmentStatus: building.id === action.payload ? 'in-progress' : building.alignmentStatus
        }))
      };
    
    case 'SET_BASE_FLOOR':
      return {
        ...state,
        buildings: state.buildings.map(building =>
          building.id === action.payload.buildingId
            ? {
                ...building,
                baseFloorId: action.payload.floorId,
                floors: building.floors.map(floor => ({
                  ...floor,
                  isBaseFloor: floor.id === action.payload.floorId,
                  visible: floor.id === action.payload.floorId ? true : floor.visible
                }))
              }
            : building
        )
      };
    
    default:
      return state;
  }
}

interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  actions: {
    setPage: (page: AppPage) => void;
    addBuilding: (building: Building) => void;
    selectFloor: (buildingId: string, floorId: string) => void;
    toggleSidebarCollapse: () => void;
    toggleSidebarPin: () => void;
    toggleMinimap: () => void;
    setMetadataPanel: (open: boolean) => void;
    setToolbarPosition: (position: { x: number; y: number }) => void;
    setMapTransform: (transform: Transform) => void;
    setSearchTerm: (term: string) => void;
    toggleBuildingExpansion: (buildingId: string) => void;
    toggleFloorVisibility: (buildingId: string, floorId: string) => void;
    updateFloorOpacity: (buildingId: string, floorId: string, opacity: number) => void;
    toggleFloorLock: (buildingId: string, floorId: string) => void;
    setDragging: (isDragging: boolean) => void;
    setLoading: (isLoading: boolean) => void;
    updateFloor: (buildingId: string, floorId: string, updates: Partial<Floor>) => void;
    uploadFloorPlan: (buildingId: string, floorId: string, file: File) => void;
    startAlignmentWorkflow: () => void;
    completeBuildingAlignment: (buildingId: string) => void;
    setCurrentAlignmentBuilding: (buildingId: string | null) => void;
    setBaseFloor: (buildingId: string, floorId: string) => void;
  };
  computed: {
    visibleFloors: Floor[];
    filteredBuildings: Building[];
    selectedBuilding: Building | null;
    totalFloors: number;
    visibleFloorCount: number;
    currentAlignmentBuilding: Building | null;
    completedBuildings: Building[];
    pendingBuildings: Building[];
    canStartAlignment: boolean;
    isAlignmentComplete: boolean;
  };
}

const AppContext = createContext<AppContextType | null>(null);

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  const actions = useMemo(() => ({
    setPage: (page: AppPage) => dispatch({ type: 'SET_PAGE', payload: page }),
    addBuilding: (building: Building) => dispatch({ type: 'ADD_BUILDING', payload: building }),
    selectFloor: (buildingId: string, floorId: string) => {
      const building = state.buildings.find(b => b.id === buildingId);
      const floor = building?.floors.find(f => f.id === floorId);
      if (floor) {
        dispatch({ type: 'SELECT_FLOOR', payload: { buildingId, floor } });
      }
    },
    toggleSidebarCollapse: () => dispatch({ type: 'TOGGLE_SIDEBAR_COLLAPSE' }),
    toggleSidebarPin: () => dispatch({ type: 'TOGGLE_SIDEBAR_PIN' }),
    toggleMinimap: () => dispatch({ type: 'TOGGLE_MINIMAP' }),
    setMetadataPanel: (open: boolean) => dispatch({ type: 'SET_METADATA_PANEL', payload: open }),
    setToolbarPosition: (position: { x: number; y: number }) => 
      dispatch({ type: 'SET_TOOLBAR_POSITION', payload: position }),
    setMapTransform: (transform: Transform) => 
      dispatch({ type: 'SET_MAP_TRANSFORM', payload: transform }),
    setSearchTerm: (term: string) => dispatch({ type: 'SET_SEARCH_TERM', payload: term }),
    toggleBuildingExpansion: (buildingId: string) => 
      dispatch({ type: 'TOGGLE_BUILDING_EXPANSION', payload: buildingId }),
    toggleFloorVisibility: (buildingId: string, floorId: string) => 
      dispatch({ type: 'TOGGLE_FLOOR_VISIBILITY', payload: { buildingId, floorId } }),
    updateFloorOpacity: (buildingId: string, floorId: string, opacity: number) => 
      dispatch({ type: 'UPDATE_FLOOR_OPACITY', payload: { buildingId, floorId, opacity } }),
    toggleFloorLock: (buildingId: string, floorId: string) => 
      dispatch({ type: 'TOGGLE_FLOOR_LOCK', payload: { buildingId, floorId } }),
    setDragging: (isDragging: boolean) => dispatch({ type: 'SET_DRAGGING', payload: isDragging }),
    setLoading: (isLoading: boolean) => dispatch({ type: 'SET_LOADING', payload: isLoading }),
    updateFloor: (buildingId: string, floorId: string, updates: Partial<Floor>) => 
      dispatch({ type: 'UPDATE_FLOOR', payload: { buildingId, floorId, updates } }),
    uploadFloorPlan: (buildingId: string, floorId: string, file: File) => 
      dispatch({ type: 'UPLOAD_FLOOR_PLAN', payload: { buildingId, floorId, file } }),
    startAlignmentWorkflow: () => dispatch({ type: 'START_ALIGNMENT_WORKFLOW' }),
    completeBuildingAlignment: (buildingId: string) => 
      dispatch({ type: 'COMPLETE_BUILDING_ALIGNMENT', payload: buildingId }),
    setCurrentAlignmentBuilding: (buildingId: string | null) => 
      dispatch({ type: 'SET_CURRENT_ALIGNMENT_BUILDING', payload: buildingId }),
    setBaseFloor: (buildingId: string, floorId: string) => 
      dispatch({ type: 'SET_BASE_FLOOR', payload: { buildingId, floorId } }),
  }), [state.buildings]);

  const computed = useMemo(() => ({
    visibleFloors: state.buildings.flatMap(building => 
      building.floors.filter(floor => floor.visible)
    ),
    filteredBuildings: state.buildings.map(building => ({
      ...building,
      floors: building.floors.filter(floor => 
        building.name.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
        floor.name.toLowerCase().includes(state.searchTerm.toLowerCase())
      )
    })).filter(building => building.floors.length > 0),
    selectedBuilding: state.selectedFloor 
      ? state.buildings.find(b => b.id === state.selectedFloor!.buildingId) || null
      : null,
    totalFloors: state.buildings.reduce((count, building) => count + building.floors.length, 0),
    visibleFloorCount: state.buildings.reduce((count, building) => 
      count + building.floors.filter(floor => floor.visible).length, 0
    ),
    currentAlignmentBuilding: state.currentAlignmentBuildingId 
      ? state.buildings.find(b => b.id === state.currentAlignmentBuildingId) || null
      : null,
    completedBuildings: state.buildings.filter(b => b.alignmentStatus === 'completed'),
    pendingBuildings: state.buildings.filter(b => b.alignmentStatus === 'pending'),
    canStartAlignment: state.buildings.some(building => 
      building.floors.some(floor => floor.uploadedFile)
    ),
    isAlignmentComplete: state.alignmentProgress >= 100,
  }), [state.buildings, state.searchTerm, state.selectedFloor, state.currentAlignmentBuildingId, state.alignmentProgress]);

  const contextValue = useMemo(() => ({
    state,
    dispatch,
    actions,
    computed
  }), [state, actions, computed]);

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}