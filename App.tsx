import React from 'react';
import { AppProvider, useApp } from './contexts/AppContext';
import { FloorUploadPage } from './components/FloorUploadPage';
import { AlignmentPage } from './components/AlignmentPage';
import { Toaster } from './components/ui/sonner';

function Router() {
  const { state } = useApp();

  switch (state.currentPage) {
    case 'upload':
      return <FloorUploadPage />;
    case 'align':
      return <AlignmentPage />;
    default:
      return <FloorUploadPage />;
  }
}

export default function App() {
  return (
    <AppProvider>
      <Router />
      
      {/* Toast Notifications */}
      <Toaster 
        position="bottom-right"
        toastOptions={{
          duration: 3000,
          className: 'bg-card border-border text-card-foreground',
        }}
      />
    </AppProvider>
  );
}