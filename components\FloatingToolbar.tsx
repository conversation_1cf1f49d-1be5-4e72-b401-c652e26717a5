import React, { useState } from 'react';
import { 
  Move, 
  RotateCw, 
  Lock, 
  Unlock, 
  AlignCenter, 
  Grid3X3, 
  Eye, 
  Undo, 
  Redo,
  GripVertical
} from 'lucide-react';
import { Button } from './ui/button';
import { Slider } from './ui/slider';
import { Separator } from './ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';

interface Position {
  x: number;
  y: number;
}

interface FloatingToolbarProps {
  position: Position;
  onPositionChange: (position: Position) => void;
  selectedFloor: { buildingId: string; floor: any } | null;
  onOpacityChange: (opacity: number) => void;
  onLockToggle: () => void;
}

export function FloatingToolbar({
  position,
  onPositionChange,
  selectedFloor,
  onOpacityChange,
  onLockToggle
}: FloatingToolbarProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [snapToGrid, setSnapToGrid] = useState(false);
  const [rotationMode, setRotationMode] = useState(false);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target !== e.currentTarget) return;
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;
    
    onPositionChange({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragStart]);

  const handleAlign = () => {
    // TODO: Implement alignment logic
    console.log('Align floors');
  };

  const handleRotate = () => {
    // TODO: Implement rotation logic
    console.log('Rotate floor');
  };

  const handleUndo = () => {
    // TODO: Implement undo logic
    console.log('Undo');
  };

  const handleRedo = () => {
    // TODO: Implement redo logic
    console.log('Redo');
  };

  return (
    <TooltipProvider>
      <div
        className="absolute bg-card/95 backdrop-blur-sm border border-border rounded-lg shadow-lg p-2 cursor-move select-none"
        style={{ left: position.x, top: position.y }}
        onMouseDown={handleMouseDown}
      >
        {/* Drag Handle */}
        <div className="flex items-center justify-center mb-2 p-1 hover:bg-accent rounded cursor-grab active:cursor-grabbing">
          <GripVertical className="w-4 h-4 text-muted-foreground" />
        </div>

        <div className="flex items-center space-x-1">
          {/* Undo/Redo */}
          <div className="flex items-center space-x-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8"
                  onClick={handleUndo}
                >
                  <Undo className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Undo</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8"
                  onClick={handleRedo}
                >
                  <Redo className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Redo</TooltipContent>
            </Tooltip>
          </div>

          <Separator orientation="vertical" className="h-8" />

          {/* Transform Tools */}
          <div className="flex items-center space-x-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8"
                  disabled={!selectedFloor}
                >
                  <Move className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Move Tool</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={rotationMode ? "default" : "ghost"}
                  size="icon"
                  className="w-8 h-8"
                  onClick={() => setRotationMode(!rotationMode)}
                  disabled={!selectedFloor}
                >
                  <RotateCw className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Rotate Tool</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8"
                  onClick={handleAlign}
                  disabled={!selectedFloor}
                >
                  <AlignCenter className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Auto Align</TooltipContent>
            </Tooltip>
          </div>

          <Separator orientation="vertical" className="h-8" />

          {/* Snap and Lock */}
          <div className="flex items-center space-x-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={snapToGrid ? "default" : "ghost"}
                  size="icon"
                  className="w-8 h-8"
                  onClick={() => setSnapToGrid(!snapToGrid)}
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Snap to Grid</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8"
                  onClick={onLockToggle}
                  disabled={!selectedFloor}
                >
                  {selectedFloor?.floor.locked ? (
                    <Lock className="w-4 h-4" />
                  ) : (
                    <Unlock className="w-4 h-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {selectedFloor?.floor.locked ? 'Unlock Floor' : 'Lock Floor'}
              </TooltipContent>
            </Tooltip>
          </div>

          <Separator orientation="vertical" className="h-8" />

          {/* Opacity Control */}
          {selectedFloor && (
            <div className="flex items-center space-x-2 px-2">
              <Eye className="w-4 h-4 text-muted-foreground" />
              <div className="w-20">
                <Slider
                  value={[selectedFloor.floor.opacity * 100]}
                  onValueChange={(value) => onOpacityChange(value[0] / 100)}
                  max={100}
                  min={0}
                  step={5}
                  className="w-full"
                />
              </div>
              <span className="text-xs text-muted-foreground w-8">
                {Math.round(selectedFloor.floor.opacity * 100)}%
              </span>
            </div>
          )}
        </div>

        {/* Floor Info */}
        {selectedFloor && (
          <div className="mt-2 pt-2 border-t border-border">
            <div className="text-xs text-muted-foreground text-center">
              {selectedFloor.floor.name}
              {selectedFloor.floor.locked && (
                <span className="ml-1">🔒</span>
              )}
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}