{"name": "georeferencing-dashboard-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@radix-ui/react-checkbox": "1.1.4", "@radix-ui/react-dialog": "1.1.6", "@radix-ui/react-label": "2.1.2", "@radix-ui/react-navigation-menu": "1.2.5", "@radix-ui/react-select": "2.1.6", "@radix-ui/react-slot": "1.1.2", "@radix-ui/react-toggle": "1.1.2", "@radix-ui/react-tooltip": "1.1.8", "class-variance-authority": "0.7.1", "embla-carousel-react": "8.6.0", "lucide-react": "0.487.0", "next-themes": "0.4.6", "react-hook-form": "7.55.0", "recharts": "2.15.2", "sonner": "2.0.3", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2", "vite": "^5.0.8"}}