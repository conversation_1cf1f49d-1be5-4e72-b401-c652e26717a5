import React, { useState } from 'react';
import { Search, Building2, Eye, EyeOff, Pin, PinOff, ChevronDown, ChevronRight } from 'lucide-react';
import { Input } from './ui/input';
import { Button } from './ui/button';
import { Separator } from './ui/separator';
import { Badge } from './ui/badge';

interface Floor {
  id: string;
  name: string;
  level: number;
  visible: boolean;
  opacity: number;
  locked: boolean;
}

interface Building {
  id: string;
  name: string;
  floors: Floor[];
}

interface CollapsibleSidebarProps {
  buildings: Building[];
  collapsed: boolean;
  pinned: boolean;
  onToggleCollapse: () => void;
  onTogglePin: () => void;
  onFloorVisibilityToggle: (buildingId: string, floorId: string) => void;
  onFloorSelect: (buildingId: string, floorId: string) => void;
}

export function CollapsibleSidebar({
  buildings,
  collapsed,
  pinned,
  onToggleCollapse,
  onTogglePin,
  onFloorVisibilityToggle,
  onFloorSelect
}: CollapsibleSidebarProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedBuildings, setExpandedBuildings] = useState<Set<string>>(new Set(['building-1']));
  const [hoveredBuilding, setHoveredBuilding] = useState<string | null>(null);

  const toggleBuildingExpansion = (buildingId: string) => {
    setExpandedBuildings(prev => {
      const newSet = new Set(prev);
      if (newSet.has(buildingId)) {
        newSet.delete(buildingId);
      } else {
        newSet.add(buildingId);
      }
      return newSet;
    });
  };

  const filteredBuildings = buildings.map(building => ({
    ...building,
    floors: building.floors.filter(floor => 
      building.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      floor.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(building => building.floors.length > 0);

  const visibleFloorCount = buildings.reduce((count, building) => 
    count + building.floors.filter(floor => floor.visible).length, 0
  );

  return (
    <div className={`bg-sidebar border-r border-sidebar-border transition-all duration-300 flex ${
      collapsed ? 'w-16' : 'w-80'
    }`}>
      {/* Icon-only sidebar when collapsed */}
      {collapsed && (
        <div className="w-16 flex flex-col p-2 space-y-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleCollapse}
            className="w-12 h-12"
            title="Expand Sidebar"
          >
            <Building2 className="w-5 h-5" />
          </Button>
          
          <Separator />
          
          {buildings.slice(0, 3).map((building, index) => (
            <Button
              key={building.id}
              variant="ghost"
              size="icon"
              className="w-12 h-12 relative"
              onMouseEnter={() => setHoveredBuilding(building.id)}
              onMouseLeave={() => setHoveredBuilding(null)}
              title={building.name}
            >
              <Building2 className="w-4 h-4" />
              {building.floors.some(f => f.visible) && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full" />
              )}
            </Button>
          ))}
        </div>
      )}

      {/* Expanded sidebar */}
      {!collapsed && (
        <div className="flex-1 flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-sidebar-border">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-sidebar-foreground">Buildings & Floors</h2>
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onTogglePin}
                  className="w-8 h-8"
                  title={pinned ? "Unpin Sidebar" : "Pin Sidebar"}
                >
                  {pinned ? <Pin className="w-4 h-4" /> : <PinOff className="w-4 h-4" />}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onToggleCollapse}
                  className="w-8 h-8"
                  title="Collapse Sidebar"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
            
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search buildings and floors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* Stats */}
            <div className="flex items-center justify-between mt-3">
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">{buildings.length} Buildings</Badge>
                <Badge variant="outline">{visibleFloorCount} Visible</Badge>
              </div>
            </div>
          </div>

          {/* Building List */}
          <div className="flex-1 overflow-y-auto p-2">
            {filteredBuildings.map((building) => (
              <div key={building.id} className="mb-2">
                <Button
                  variant="ghost"
                  className="w-full justify-start p-2 h-auto"
                  onClick={() => toggleBuildingExpansion(building.id)}
                >
                  <div className="flex items-center space-x-2 w-full">
                    {expandedBuildings.has(building.id) ? (
                      <ChevronDown className="w-4 h-4 text-muted-foreground" />
                    ) : (
                      <ChevronRight className="w-4 h-4 text-muted-foreground" />
                    )}
                    <Building2 className="w-4 h-4 text-sidebar-foreground" />
                    <span className="text-left flex-1">{building.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {building.floors.filter(f => f.visible).length}/{building.floors.length}
                    </Badge>
                  </div>
                </Button>

                {/* Floor List */}
                {expandedBuildings.has(building.id) && (
                  <div className="ml-6 mt-1 space-y-1">
                    {building.floors.map((floor) => (
                      <div
                        key={floor.id}
                        className="flex items-center space-x-2 p-2 rounded-md hover:bg-sidebar-accent group"
                      >
                        <Button
                          variant="ghost"
                          size="icon"
                          className="w-6 h-6"
                          onClick={() => onFloorVisibilityToggle(building.id, floor.id)}
                          title={floor.visible ? "Hide Floor" : "Show Floor"}
                        >
                          {floor.visible ? (
                            <Eye className="w-3 h-3" />
                          ) : (
                            <EyeOff className="w-3 h-3 text-muted-foreground" />
                          )}
                        </Button>
                        
                        <button
                          className="flex-1 text-left text-sm hover:text-sidebar-primary"
                          onClick={() => onFloorSelect(building.id, floor.id)}
                        >
                          {floor.name}
                        </button>
                        
                        <div className="flex items-center space-x-1">
                          <Badge variant={floor.level >= 0 ? "default" : "secondary"} className="text-xs px-1">
                            {floor.level >= 0 ? `L${floor.level}` : `B${Math.abs(floor.level)}`}
                          </Badge>
                          {floor.locked && (
                            <div className="w-3 h-3 bg-muted-foreground rounded-full" title="Locked" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}