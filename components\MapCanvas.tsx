import React, { useRef, useEffect, useState } from 'react';

interface Floor {
  id: string;
  name: string;
  level: number;
  visible: boolean;
  opacity: number;
  locked: boolean;
}

interface Building {
  id: string;
  name: string;
  floors: Floor[];
}

interface Transform {
  x: number;
  y: number;
  scale: number;
}

interface MapCanvasProps {
  buildings: Building[];
  selectedFloor: { buildingId: string; floor: Floor } | null;
  transform: Transform;
  onTransformChange: (transform: Transform) => void;
  onFloorSelect: (buildingId: string, floorId: string) => void;
}

// Mock floor plan data with different colors for each building
const mockFloorPlans = {
  'floor-1-1': { x: 100, y: 100, width: 200, height: 150, color: '#3b82f6' },
  'floor-1-2': { x: 120, y: 80, width: 200, height: 150, color: '#3b82f6' },
  'floor-1-3': { x: 140, y: 60, width: 200, height: 150, color: '#3b82f6' },
  'floor-2-1': { x: 400, y: 200, width: 180, height: 120, color: '#10b981' },
  'floor-2-2': { x: 420, y: 180, width: 180, height: 120, color: '#10b981' },
  'floor-3-1': { x: 250, y: 350, width: 160, height: 100, color: '#f59e0b' },
  'floor-3-2': { x: 270, y: 370, width: 160, height: 100, color: '#f59e0b' },
};

export function MapCanvas({
  buildings,
  selectedFloor,
  transform,
  onTransformChange,
  onFloorSelect
}: MapCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [draggedFloor, setDraggedFloor] = useState<string | null>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Apply transform
    ctx.save();
    ctx.translate(transform.x, transform.y);
    ctx.scale(transform.scale, transform.scale);

    // Draw grid
    drawGrid(ctx, canvas.width, canvas.height, transform);

    // Draw floor plans
    buildings.forEach(building => {
      building.floors.forEach(floor => {
        if (floor.visible) {
          drawFloorPlan(ctx, building.id, floor);
        }
      });
    });

    // Highlight selected floor
    if (selectedFloor && selectedFloor.floor.visible) {
      highlightFloor(ctx, selectedFloor.buildingId, selectedFloor.floor);
    }

    ctx.restore();
  }, [buildings, selectedFloor, transform]);

  const drawGrid = (ctx: CanvasRenderingContext2D, width: number, height: number, transform: Transform) => {
    const gridSize = 50 * transform.scale;
    if (gridSize < 10) return; // Don't draw grid if too small

    ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
    ctx.lineWidth = 1;

    // Vertical lines
    for (let x = -transform.x % gridSize; x < width; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x / transform.scale, -transform.y / transform.scale);
      ctx.lineTo(x / transform.scale, (height - transform.y) / transform.scale);
      ctx.stroke();
    }

    // Horizontal lines
    for (let y = -transform.y % gridSize; y < height; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(-transform.x / transform.scale, y / transform.scale);
      ctx.lineTo((width - transform.x) / transform.scale, y / transform.scale);
      ctx.stroke();
    }
  };

  const drawFloorPlan = (ctx: CanvasRenderingContext2D, buildingId: string, floor: Floor) => {
    const planData = mockFloorPlans[floor.id as keyof typeof mockFloorPlans];
    if (!planData) return;

    ctx.globalAlpha = floor.opacity;
    
    // Draw floor rectangle
    ctx.fillStyle = planData.color;
    ctx.fillRect(planData.x, planData.y, planData.width, planData.height);

    // Draw border
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.strokeRect(planData.x, planData.y, planData.width, planData.height);

    // Draw floor label
    ctx.fillStyle = '#ffffff';
    ctx.font = '14px Inter, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(
      floor.name,
      planData.x + planData.width / 2,
      planData.y + planData.height / 2
    );

    // Draw level indicator
    ctx.font = '12px Inter, sans-serif';
    ctx.fillText(
      floor.level >= 0 ? `L${floor.level}` : `B${Math.abs(floor.level)}`,
      planData.x + planData.width / 2,
      planData.y + planData.height / 2 + 20
    );

    ctx.globalAlpha = 1;
  };

  const highlightFloor = (ctx: CanvasRenderingContext2D, buildingId: string, floor: Floor) => {
    const planData = mockFloorPlans[floor.id as keyof typeof mockFloorPlans];
    if (!planData) return;

    ctx.strokeStyle = '#ff6b6b';
    ctx.lineWidth = 3;
    ctx.setLineDash([5, 5]);
    ctx.strokeRect(planData.x - 2, planData.y - 2, planData.width + 4, planData.height + 4);
    ctx.setLineDash([]);
  };

  const getFloorAtPoint = (x: number, y: number): { buildingId: string; floorId: string } | null => {
    // Convert screen coordinates to canvas coordinates
    const canvasX = (x - transform.x) / transform.scale;
    const canvasY = (y - transform.y) / transform.scale;

    for (const building of buildings) {
      for (const floor of building.floors) {
        if (!floor.visible) continue;
        
        const planData = mockFloorPlans[floor.id as keyof typeof mockFloorPlans];
        if (!planData) continue;

        if (
          canvasX >= planData.x &&
          canvasX <= planData.x + planData.width &&
          canvasY >= planData.y &&
          canvasY <= planData.y + planData.height
        ) {
          return { buildingId: building.id, floorId: floor.id };
        }
      }
    }
    return null;
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const floorAtPoint = getFloorAtPoint(x, y);
    
    if (floorAtPoint) {
      // Select floor
      onFloorSelect(floorAtPoint.buildingId, floorAtPoint.floorId);
      setDraggedFloor(floorAtPoint.floorId);
    }

    setIsDragging(true);
    setDragStart({ x: x - transform.x, y: y - transform.y });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    if (draggedFloor) {
      // TODO: Implement floor dragging logic
      // For now, just pan the canvas
    }

    onTransformChange({
      ...transform,
      x: x - dragStart.x,
      y: y - dragStart.y
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setDraggedFloor(null);
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
    const newScale = Math.max(0.1, Math.min(5, transform.scale * zoomFactor));

    // Zoom towards mouse position
    const newX = mouseX - (mouseX - transform.x) * (newScale / transform.scale);
    const newY = mouseY - (mouseY - transform.y) * (newScale / transform.scale);

    onTransformChange({
      x: newX,
      y: newY,
      scale: newScale
    });
  };

  return (
    <div className="relative w-full h-full bg-muted/20">
      <canvas
        ref={canvasRef}
        className="w-full h-full cursor-grab active:cursor-grabbing"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onWheel={handleWheel}
      />
      
      {/* Canvas Info */}
      <div className="absolute top-4 left-4 bg-card/90 backdrop-blur-sm border border-border rounded-lg p-3 shadow-lg">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <span className="text-muted-foreground">Zoom:</span>
            <span>{Math.round(transform.scale * 100)}%</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-muted-foreground">Position:</span>
            <span>{Math.round(transform.x)}, {Math.round(transform.y)}</span>
          </div>
          {selectedFloor && (
            <div className="flex items-center space-x-2">
              <span className="text-muted-foreground">Selected:</span>
              <span>{selectedFloor.floor.name}</span>
            </div>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="absolute bottom-4 left-4 bg-card/90 backdrop-blur-sm border border-border rounded-lg p-3 shadow-lg">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <kbd className="px-2 py-1 bg-muted rounded text-xs">Click</kbd>
            <span>Select floor</span>
          </div>
          <div className="flex items-center space-x-2">
            <kbd className="px-2 py-1 bg-muted rounded text-xs">Drag</kbd>
            <span>Pan view</span>
          </div>
          <div className="flex items-center space-x-2">
            <kbd className="px-2 py-1 bg-muted rounded text-xs">Scroll</kbd>
            <span>Zoom</span>
          </div>
        </div>
      </div>
    </div>
  );
}